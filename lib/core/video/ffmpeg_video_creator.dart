import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:flutter/services.dart' show rootBundle;

import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:convert';

/// Copies the Ethnocentric font from assets to a temporary directory for FFmpeg use.
Future<String> _copyFontToTemp() async {
  final fontData = await rootBundle.load('assets/fonts/ethnocentric_regular.otf');
  final tempDir = await getTemporaryDirectory();
  final fontPath = '${tempDir.path}/ethnocentric_regular.otf';
  final fontFile = File(fontPath);
  await fontFile.writeAsBytes(fontData.buffer.asUint8List());
  return fontPath;
}

/// Parses the lyrics JSON file to extract lines with timestamps.
Future<List<Map<String, dynamic>>> _parseLyrics(String jsonPath) async {
  final jsonString = await File(jsonPath).readAsString();
  final jsonData = jsonDecode(jsonString);
  return List<Map<String, dynamic>>.from(jsonData['lyrics']['data']);
}

/// Converts time from "minutes:seconds:milliseconds" format to seconds.
double _parseTime(String timeStr) {
  final parts = timeStr.split(':');
  final minutes = int.parse(parts[0]);
  final seconds = int.parse(parts[1]);
  final milliseconds = int.parse(parts[2]);
  return minutes * 60 + seconds + milliseconds / 1000.0;
}

/// Creates a video using FFmpeg with the specified inputs and requirements.
Future<void> createVideo({
  required String audioPath, // Path to FLAC audio file
  required String userImagePath, // Path to JPEG user image
  required String brandLogoPath, // Path to PNG brand logo
  required String lyricsJsonPath, // Path to lyrics JSON file
  required String trackName, // Track name string
  required String username, // Username string
}) async {
  // Prepare assets
  final fontPath = await _copyFontToTemp();
  final lyrics = await _parseLyrics(lyricsJsonPath);
  final tempDir = await getTemporaryDirectory();
  final outputPath = '${tempDir.path}/output.mp4';

  // Build FFmpeg filter_complex string
  String filterComplex = """
[1:v]scale=500:500[user];
[2:v]scale=100:100[logo];
[user][logo]overlay=400:400[composite];
[0:v][composite]overlay=(main_w-500)/2:0.35*main_h[bg_with_image];
[bg_with_image]drawtext=fontfile='$fontPath':text='$trackName':fontcolor=white:fontsize=24:x=(w-tw)/2:y=1222[with_track];
[with_track]drawtext=fontfile='$fontPath':text='By':fontcolor=white:fontsize=24:x=(w-tw)/2:y=1252[with_by];
[with_by]drawtext=fontfile='$fontPath':text='$username':fontcolor=white:fontsize=24:x=(w-tw)/2:y=1282[with_user];
""";

  String prev = '[with_user]';
  for (int i = 0; i < lyrics.length; i++) {
    final line = lyrics[i];
    final text = line['text'].replaceAll("'", "\\'"); // Escape single quotes for FFmpeg
    final start = _parseTime(line['start_time']);
    final end = _parseTime(line['end_time']);
    filterComplex += "${prev}drawtext=fontfile='$fontPath':text='$text':fontcolor=white:fontsize=20:x=(w-tw)/2:y=0.7*h:enable='between(t,$start,$end)'[lyrics$i];";
    prev = '[lyrics$i]';
  }

  final finalOutput = prev;

  // Construct FFmpeg command
  final command = """
-f lavfi -i color=c=black:s=1080x1920:r=30
-i "$userImagePath" \
-i "$brandLogoPath" \
-i "$audioPath" \
-filter_complex "$filterComplex" \
-map "$finalOutput" -map 3:a \
-c:v libx264 -b:v 500k -c:a aac -b:a 96k -shortest "$outputPath"
""";

  // Execute FFmpeg command
  final session = await FFmpegKit.execute(command);
  final returnCode = await session.getReturnCode();

  if (ReturnCode.isSuccess(returnCode)) {
    print("Video created successfully at $outputPath");
  } else {
    final logs = await session.getAllLogsAsString();
    print("Error creating video: $logs");
    throw Exception("Failed to create video");
  }
}
